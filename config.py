import os
import torch

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 全局调试配置
VERBOSE_OUTPUT = True  # 是否输出详细的调试信息

# 环境配置
ENV_CONFIG = {
    'file_path': os.path.join(ROOT_DIR, 'data', 'active_load_profile.csv'),  # 负荷数据文件
    'gen_schedule_file': os.path.join(ROOT_DIR, 'data', 'power_schedule_with_wind_sequential.csv'),  # 发电机调度数据文件
    'look_ahead': 1,  # 修改为1，实现实时调度
    'debug': VERBOSE_OUTPUT,  # 使用全局调试配置
    'wind_power_enabled': True,   # 启用风力发电

    # 平衡机配置
    'enable_slack_bus': True,  # 是否启用平衡机功能
}

# 智能体配置
AGENT_CONFIG = {
    # === 学习率配置：保守的学习策略 ===
    'actor_lr': 1e-5,  # 适当提高Actor学习率
    'critic_lr': 5e-5,  # 保持Critic学习率不变
    
    # === 强化学习基础参数 ===
    'gamma': 0.99,  # 降低折扣因子，更关注即时奖励
    'tau': 0.003,   # 减小软更新系数，提高目标网络稳定性
    'buffer_size': 15000,  # 适当减小缓冲区，提高样本新鲜度
    'batch_size': 64,      # 进一步减小批次大小，确保早期能够更新
    'hidden_size': 256,    # 保持隐藏层大小
    
    # === 探索策略配置：延长探索期 ===
    'initial_noise_scale': 0.2,    # 提高初始噪声强度
    'final_noise_scale': 0.02,     # 保持最终噪声不为0
    'exploration_steps': 600,      # 调回探索期 600 步
    
    # === 监督学习配置：延长监督指导期 ===
    'initial_supervision_weight': 0.7,  # 提高初始监督权重
    'final_supervision_weight': 0.2,   # A1: 提高最终监督权重，保留后期引导
    'supervision_decay_early_ratio': 0.4,  # B1: 延长监督学习有效期
    
    # === 损失权重与梯度裁剪：稳定训练核心 ===
    'actor_loss_weight': 0.1,  # 提升Actor损失权重，使梯度信号更明显
    'critic_loss_weight': 0.5,  # A2: 降低Critic损失权重，防止Q值过大
    'grad_clip_norm': 0.5,      # 梯度裁剪范数，防止梯度爆炸原本是0.5
    
    'verbose': VERBOSE_OUTPUT,  # 使用全局调试配置
}

# 训练配置
TRAIN_CONFIG = {
    'num_episodes': 1200, 
    'verbose': VERBOSE_OUTPUT  # 使用全局调试配置
}

# 测试配置
TEST_CONFIG = {
    'num_episodes': 1,
    'verbose': VERBOSE_OUTPUT  # 使用全局调试配置
}

# 模型保存路径
MODEL_PATHS = {
    'model_dir': os.path.join(ROOT_DIR, 'models'),
    'actor_path': os.path.join(ROOT_DIR, 'models', 'ddpg_actor.pth'),
    'critic_path': os.path.join(ROOT_DIR, 'models', 'ddpg_critic.pth'),
    'best_actor_path': os.path.join(ROOT_DIR, 'models', 'best_actor.pth'),
    'best_critic_path': os.path.join(ROOT_DIR, 'models', 'best_critic.pth')
}

# 图表保存路径
PLOT_CONFIG = {
    'output_dir': os.path.join(ROOT_DIR, 'plots'),
    'reward_plot': 'training_reward_curve.png',
    'loss_plot': 'loss_curves.png',
    'weight_plot': 'supervision_weight_curve.png'
}

# 确保所有配置都能被导出
__all__ = ['ENV_CONFIG', 'AGENT_CONFIG', 'TRAIN_CONFIG', 'MODEL_PATHS', 'PLOT_CONFIG', 'DEVICE', 'VERBOSE_OUTPUT']