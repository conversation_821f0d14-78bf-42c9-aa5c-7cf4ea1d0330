#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控指标功能的脚本
"""

import numpy as np
import torch
import os
import sys
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent, OptimalActionProvider
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from utils.common import plot_monitoring_curves
from config import ENV_CONFIG, AGENT_CONFIG, TRAIN_CONFIG, MODEL_PATHS, PLOT_CONFIG, DEVICE, VERBOSE_OUTPUT

def test_monitoring():
    """测试监控指标收集和可视化功能"""
    print("开始测试监控指标功能...")
    
    # 1. 创建环境和智能体
    env = IEEE30Env(**ENV_CONFIG)
    state = env.reset()
    state_dim = len(state)
    action_dim = env.action_space.shape[0]
    
    # 初始化智能体
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    # 设置环境引用
    agent.set_env(env)
    agent.env_graph_builder = env.graph_builder
    
    print(f"环境和智能体初始化完成")
    print(f"状态维度: {state_dim}, 动作维度: {action_dim}")
    
    # 2. 先手动添加一些经验到缓冲区，然后测试update方法
    print("手动添加经验到缓冲区并测试update方法...")

    # 运行一个episode来收集经验
    state = env.reset()
    agent.start_episode()

    for step in range(100):  # 运行100步来确保有足够的经验
        # 选择动作
        action = agent.select_action(state, env.graph_builder, add_noise=True)

        # 执行动作
        next_state, reward, done, info = env.step(action)
        if next_state is None:
            next_state = state

        # 转换为图数据
        expected_node_feature_dim = state.shape[0] // agent.time_steps
        graph_list = state_to_graph(
            state,
            env.graph_builder,
            time_steps=agent.time_steps,
            node_feature_dim=expected_node_feature_dim
        )

        next_graph_list = None
        if not done:
            next_graph_list = state_to_graph(
                next_state,
                env.graph_builder,
                time_steps=agent.time_steps,
                node_feature_dim=expected_node_feature_dim
            )

        # 存储经验
        adjusted_action = info.get('adjusted_action', action)
        original_reward, normalized_reward = agent.store_transition(
            graph_list, adjusted_action, reward,
            next_graph_list if not done else None, done, info
        )

        state = next_state

        if done:
            state = env.reset()
            done = False

    # 结束episode
    agent.end_episode()

    # 检查缓冲区大小
    buffer_size = len(agent.replay_buffer)
    print(f"缓冲区大小: {buffer_size}, 批次大小: {agent.batch_size}")

    # 强制调用update方法多次来收集监控数据
    if buffer_size > 0:
        print("强制调用update方法来收集监控数据...")
        for i in range(10):  # 调用10次update
            try:
                actor_loss, critic_loss = agent.update()
                print(f"Update {i+1}: Actor Loss: {actor_loss:.4f}, Critic Loss: {critic_loss:.4f}")
            except Exception as e:
                print(f"Update {i+1} 失败: {e}")
    else:
        print("缓冲区为空，无法进行更新")
    
    # 3. 检查监控数据是否收集成功
    print("\n检查监控数据收集情况:")
    
    if hasattr(agent, 'q_value_history') and agent.q_value_history:
        print(f"✓ Q值历史数据: {len(agent.q_value_history)} 条记录")
        print(f"  最新Q值统计: {agent.q_value_history[-1]}")
    else:
        print("✗ Q值历史数据为空")
    
    if hasattr(agent, 'td_error_history') and agent.td_error_history:
        print(f"✓ TD误差历史数据: {len(agent.td_error_history)} 条记录")
        print(f"  最新TD误差统计: {agent.td_error_history[-1]}")
    else:
        print("✗ TD误差历史数据为空")
    
    if hasattr(agent, 'critic_grad_norm_history') and agent.critic_grad_norm_history:
        print(f"✓ Critic梯度范数历史: {len(agent.critic_grad_norm_history)} 条记录")
        print(f"  最新Critic梯度范数: {agent.critic_grad_norm_history[-1]:.4f}")
    else:
        print("✗ Critic梯度范数历史数据为空")
    
    if hasattr(agent, 'actor_grad_norm_history') and agent.actor_grad_norm_history:
        non_zero_actor_grads = [g for g in agent.actor_grad_norm_history if g > 0]
        print(f"✓ Actor梯度范数历史: {len(non_zero_actor_grads)} 条有效记录")
        if non_zero_actor_grads:
            print(f"  最新Actor梯度范数: {non_zero_actor_grads[-1]:.4f}")
    else:
        print("✗ Actor梯度范数历史数据为空")
    
    if hasattr(agent, 'action_std_history') and agent.action_std_history:
        non_zero_action_stds = [std for std in agent.action_std_history if std > 0]
        print(f"✓ 动作标准差历史: {len(non_zero_action_stds)} 条有效记录")
        if non_zero_action_stds:
            print(f"  最新动作标准差: {non_zero_action_stds[-1]:.4f}")
    else:
        print("✗ 动作标准差历史数据为空")
    
    # 4. 测试监控统计功能
    print("\n测试监控统计功能:")
    if hasattr(agent, 'get_monitoring_stats'):
        stats = agent.get_monitoring_stats()
        if stats:
            print("✓ 监控统计功能正常")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        else:
            print("✗ 监控统计返回空结果")
    else:
        print("✗ 智能体没有get_monitoring_stats方法")
    
    # 5. 测试监控曲线绘制
    print("\n测试监控曲线绘制:")
    try:
        test_output_dir = os.path.join(PLOT_CONFIG['output_dir'], 'test')
        os.makedirs(test_output_dir, exist_ok=True)
        plot_monitoring_curves(agent, test_output_dir)
        print("✓ 监控曲线绘制成功")
        print(f"  图表保存在: {test_output_dir}")
    except Exception as e:
        print(f"✗ 监控曲线绘制失败: {e}")
    
    print("\n监控指标功能测试完成!")

if __name__ == "__main__":
    test_monitoring()
