#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试监控指标功能
"""

import numpy as np
import torch
import os
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from config import AGENT_CONFIG, DEVICE

def test_monitoring_simple():
    """简单测试监控指标收集"""
    print("开始简单测试监控指标功能...")
    
    # 创建一个简单的智能体
    agent = GNNDDPGAgent(
        node_features=3,
        edge_features=1,
        action_dim=6,
        action_bound=1.0,
        time_steps=1
    )
    
    print(f"智能体创建成功")
    print(f"检查监控历史属性:")
    
    # 检查监控历史属性是否存在
    attrs_to_check = [
        'q_value_history',
        'target_q_history', 
        'td_error_history',
        'actor_grad_norm_history',
        'critic_grad_norm_history',
        'action_std_history'
    ]
    
    for attr in attrs_to_check:
        if hasattr(agent, attr):
            value = getattr(agent, attr)
            print(f"✓ {attr}: {type(value)} (长度: {len(value)})")
        else:
            print(f"✗ {attr}: 不存在")
    
    # 检查get_monitoring_stats方法
    if hasattr(agent, 'get_monitoring_stats'):
        print("✓ get_monitoring_stats方法存在")
        try:
            stats = agent.get_monitoring_stats()
            print(f"  返回类型: {type(stats)}")
            print(f"  返回内容: {stats}")
        except Exception as e:
            print(f"  调用失败: {e}")
    else:
        print("✗ get_monitoring_stats方法不存在")
    
    # 手动添加一些测试数据
    print("\n手动添加测试数据:")
    if hasattr(agent, 'q_value_history'):
        agent.q_value_history.append({'mean': -1.5, 'std': 0.5, 'min': -3.0, 'max': 0.0})
        agent.q_value_history.append({'mean': -1.2, 'std': 0.4, 'min': -2.5, 'max': 0.2})
        print(f"✓ 添加了 {len(agent.q_value_history)} 条Q值数据")
    
    if hasattr(agent, 'td_error_history'):
        agent.td_error_history.append({'mean': 0.15, 'std': 0.08, 'max': 0.5})
        agent.td_error_history.append({'mean': 0.12, 'std': 0.06, 'max': 0.4})
        print(f"✓ 添加了 {len(agent.td_error_history)} 条TD误差数据")
    
    if hasattr(agent, 'critic_grad_norm_history'):
        agent.critic_grad_norm_history.extend([0.25, 0.18])
        print(f"✓ 添加了 {len(agent.critic_grad_norm_history)} 条Critic梯度数据")
    
    if hasattr(agent, 'actor_grad_norm_history'):
        agent.actor_grad_norm_history.extend([0.15, 0.12])
        print(f"✓ 添加了 {len(agent.actor_grad_norm_history)} 条Actor梯度数据")
    
    if hasattr(agent, 'action_std_history'):
        agent.action_std_history.extend([0.3, 0.25])
        print(f"✓ 添加了 {len(agent.action_std_history)} 条动作标准差数据")
    
    # 再次测试get_monitoring_stats
    print("\n测试监控统计功能（有数据后）:")
    if hasattr(agent, 'get_monitoring_stats'):
        try:
            stats = agent.get_monitoring_stats()
            print(f"✓ 监控统计成功")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        except Exception as e:
            print(f"✗ 监控统计失败: {e}")
    
    # 测试绘图功能
    print("\n测试监控曲线绘制:")
    try:
        from utils.common import plot_monitoring_curves
        test_output_dir = "plots/simple_test"
        os.makedirs(test_output_dir, exist_ok=True)
        plot_monitoring_curves(agent, test_output_dir)
        print("✓ 监控曲线绘制成功")
    except Exception as e:
        print(f"✗ 监控曲线绘制失败: {e}")
    
    print("\n简单测试完成!")

if __name__ == "__main__":
    test_monitoring_simple()
